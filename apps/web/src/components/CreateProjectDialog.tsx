import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Plus } from "lucide-react";
import { User, UserRole } from "@/types/project";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Malaysian mobile phone number formatting and validation utilities
const formatPhoneNumber = (value: string): string => {
  // Remove all non-digits
  const numbers = value.replace(/\D/g, '');

  if (numbers.length === 0) return '';

  // Auto-add leading 0 if not present
  let cleanNumbers = numbers;
  if (!numbers.startsWith('0')) {
    cleanNumbers = '0' + numbers;
  }

  // Format Malaysian mobile numbers: 012-XXX XXXX or 012-XXXX XXXX
  if (cleanNumbers.length <= 3) return cleanNumbers;
  if (cleanNumbers.length <= 6) return `${cleanNumbers.slice(0, 3)}-${cleanNumbers.slice(3)}`;
  if (cleanNumbers.length <= 10) return `${cleanNumbers.slice(0, 3)}-${cleanNumbers.slice(3, 6)} ${cleanNumbers.slice(6)}`;

  // Handle 11-digit numbers: 012-XXXX XXXX
  if (cleanNumbers.length === 11) {
    return `${cleanNumbers.slice(0, 3)}-${cleanNumbers.slice(3, 7)} ${cleanNumbers.slice(7)}`;
  }

  // Limit to 11 digits max
  return `${cleanNumbers.slice(0, 3)}-${cleanNumbers.slice(3, 6)} ${cleanNumbers.slice(6, 10)}`;
};

const isValidPhoneNumber = (phone: string): boolean => {
  if (!phone) return true; // Empty is valid (optional field)

  // Malaysian mobile phone number patterns
  const patterns = [
    /^01[0-9]-\d{3} \d{4}$/, // 10-digit: 01X-XXX XXXX
    /^01[0-9]-\d{4} \d{4}$/, // 11-digit: 01X-XXXX XXXX
    /^01[0-9]\d{7,8}$/, // Unformatted: 01XXXXXXXX or 01XXXXXXXXX
  ];

  return patterns.some(pattern => pattern.test(phone));
};

interface CreateProjectDialogProps {
  onCreateProject: (project: {
    title: string;
    client: string;
    salesAmount: number;
    contact?: string;
    projectDate?: string;
    source?: 'WS' | 'FB' | 'XHS' | 'INSTA' | 'Website';
    vpDate?: string;
    landed?: boolean;
    remarks?: string;
  }) => void;
  currentUser: User;
  users: User[];
}

export const CreateProjectDialog = ({ onCreateProject, currentUser, users }: CreateProjectDialogProps) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    client: '',
    salesAmount: '',
    contact: '',
    projectDate: new Date().toISOString().split('T')[0], // Default to today
    source: '' as 'WS' | 'FB' | 'XHS' | 'INSTA' | 'Website' | '',
    vpDate: '',
    landed: false,
    remarks: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.client || !formData.salesAmount) {
      return; // Required fields; UI already uses required attributes
    }

    onCreateProject({
      title: formData.title,
      client: formData.client,
      salesAmount: parseInt(formData.salesAmount, 10),
      contact: formData.contact || undefined,
      projectDate: formData.projectDate || undefined,
      source: formData.source || undefined,
      vpDate: formData.vpDate || undefined,
      landed: formData.landed,
      remarks: formData.remarks || undefined,
    });

    setFormData({
      title: '',
      client: '',
      salesAmount: '',
      contact: '',
      projectDate: new Date().toISOString().split('T')[0],
      source: '' as 'WS' | 'FB' | 'XHS' | 'INSTA' | 'Website' | '',
      vpDate: '',
      landed: false,
      remarks: ''
    });
    setOpen(false);
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          New Project
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Project Title</Label>
              <Input
                id="title"
                placeholder="Enter project title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="client">Client Name</Label>
              <Input
                id="client"
                placeholder="Enter client name"
                value={formData.client}
                onChange={(e) => handleInputChange('client', e.target.value)}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="salesAmount">Sales Amount ($)</Label>
              <Input
                id="salesAmount"
                type="number"
                placeholder="Enter total sales amount"
                value={formData.salesAmount}
                onChange={(e) => handleInputChange('salesAmount', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="contact">Contact (Phone)</Label>
              <Input
                id="contact"
                type="tel"
                placeholder="************"
                value={formData.contact}
                onChange={(e) => {
                  const formatted = formatPhoneNumber(e.target.value);
                  handleInputChange('contact', formatted);
                }}
                className={`${formData.contact && !isValidPhoneNumber(formData.contact) ? 'border-red-500 focus:border-red-500' : ''}`}
                maxLength={13}
              />
              {formData.contact && !isValidPhoneNumber(formData.contact) && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <span className="text-red-500">⚠</span>
                  Please enter a valid Malaysian phone number
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="projectDate">Project Date</Label>
              <Input
                id="projectDate"
                type="date"
                value={formData.projectDate}
                onChange={(e) => handleInputChange('projectDate', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="source">Source</Label>
              <Select value={formData.source} onValueChange={(value) => handleInputChange('source', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="WS">WS</SelectItem>
                  <SelectItem value="FB">FB</SelectItem>
                  <SelectItem value="XHS">XHS</SelectItem>
                  <SelectItem value="INSTA">INSTA</SelectItem>
                  <SelectItem value="Website">Website</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="vpDate">VP Date</Label>
              <Input
                id="vpDate"
                placeholder="Enter VP date"
                value={formData.vpDate}
                onChange={(e) => handleInputChange('vpDate', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="landed"
                  checked={formData.landed}
                  onCheckedChange={(checked) => handleInputChange('landed', checked)}
                />
                <Label htmlFor="landed">Landed</Label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="remarks">Remarks</Label>
            <Textarea
              id="remarks"
              placeholder="Enter project remarks..."
              value={formData.remarks}
              onChange={(e) => handleInputChange('remarks', e.target.value)}
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit">Create Project</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};